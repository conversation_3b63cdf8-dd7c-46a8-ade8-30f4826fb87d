      <div class="row">
            <div class="col-md-6">
                  <h3>General Information</h3>

                  <!-- Weapon Type Indicator -->
                  <div class="weapon-type-container" style="margin-bottom: 15px;">
                    <div class="weapon-type-badge"
                         [ngClass]="getWeaponTypeClass()"
                         *ngIf="getWeaponType()">
                      {{ getWeaponType() }}
                    </div>
                  </div>

                  <h5>Battle Description:</h5>
                  <textarea class="form-control" style="margin-top: 10px; margin-bottom: 10px;" type="text"
                        value="{{ (weapon | translation : lstLanguage : weapon?.id : 'description') }}" #description
                        (change)="changeItemValue('description', description.value)">
      </textarea>
                  <br />
                  <h3>Base Statistics:</h3>
                  <h5><i class="pe-7s-info" style="position: relative" placement='top' delay='250' ttWidth="auto"
                              ttAlign="center" ttPadding="15px" tooltip="Defines the initial level of the weapon."></i>
                        Initial Level (WLbase): {{weapon?.wlbase}}</h5>

                  <br />
                  <h3>Modifiers</h3>
                  <div style="display: flex; flex-direction: row; gap:20px;">
                        <div>
                              <h5><i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                                          ttWidth="auto" ttAlign="center" ttPadding="15px"
                                          tooltip="Minimum IQ needed to use the weapon."></i>Minimum IQ Requirement
                                    (QI-min): {{weapon?.qiMin}}</h5>
                        </div>
                        <div>
                              <h5><i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                                          ttWidth="auto" ttAlign="center" ttPadding="15px"
                                          tooltip="Minimum luck needed to use the weapon."></i>Minimum Luck Requirement
                                    (Luck-min): {{weapon?.luckMin}} </h5>
                        </div>
                  </div>
                  <br />
                  <h3>Effects</h3>
                  <div style="display: flex; flex-direction: row; gap:20px;">
                        <div>
                              <h5>Shake:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                                          ttWidth="auto" ttAlign="center" ttPadding="15px"
                                          tooltip="Enables vibration on the enemy."></i></h5>
                              <input type="checkbox" class="toggle center" #shake [checked]="weapon?.shake"
                                    (change)="changeItemValue('shake', shake.value)" />
                        </div>
                        <div>
                              <h5>Hit:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                                          ttWidth="auto" ttAlign="center" ttPadding="15px"
                                          tooltip="Enables the visual impact effect on the enemy."></i></h5>
                              <input type="checkbox" class="toggle center" #hit [checked]="weapon?.hit"
                                    (change)="changeItemValue('hit',hit.value)" />
                        </div>
                        <div>
                              <h5>Splits:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                                          ttWidth="auto" ttAlign="center" ttPadding="15px"
                                          tooltip="Divides the visual presentation of damage numbers, emulating multiple attacks."></i>
                              </h5>
                              <input type="number" placeholder=" " class="toggle background-input-table-color" #split
                                    [value]="weapon?.split" style="margin-left: 10px;"
                                    (change)="changeItemValue('split', split.value)" />
                        </div>
                        <div>
                              <h5>Effect:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                                          ttWidth="auto" ttAlign="center" ttPadding="15px"
                                          tooltip="Type of Visual Effect (VFX)"></i></h5>
                              <select required class="dropdown filter-dropdown limited select-background-color"
                                    style="display: inline-block; margin: 10px; margin-bottom: 15px" #weaponId
                                    (change)="changeItemValue('effectId', weaponId.value)">
                                    <option *ngFor="let effect of effects" value="{{ effect.id }}">{{ effect.name }}
                                    </option>
                              </select>
                        </div>
                  </div>
            </div>

            <div class="col-md-6">
                  <h3>Targets</h3>
                  <h5>Character Targets:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                              ttWidth="auto" ttAlign="center" ttPadding="15px"
                              tooltip="Selects the characters affected by the weapon."></i></h5>
                  <character-selector (parentEvent)="ngAfterViewInit()" [currentWeapon]="this.weapon"
                        [selectedCharactersId]="weapon && weapon.charactersId"
                        (selectedCharactersIdChange)="changeSelectedId($event, 'charactersId')">
                  </character-selector>
                  <br />
                  <h5>Classes Targets:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                              ttWidth="auto" ttAlign="center" ttPadding="15px"
                              tooltip="Selects the classes affected by the weapon."></i></h5>
                  <class-selector (parentEvent)="ngAfterViewInit()" [currentWeapon]="this.weapon"
                        [selectedClassessId]="weapon && weapon.classesId"
                        (selectedClassessIdChange)="changeSelectedId($event, 'classesId')">
                  </class-selector>
            </div>

            <div class="col-md-6">
                  <h3>Users</h3>
                  <h5>Enabled Character:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                              ttWidth="auto" ttAlign="center" ttPadding="15px"
                              tooltip="Selects the characters with the ability to use the weapon."></i></h5>
                  <enabled-character-selector (parentEvent)="ngAfterViewInit()" [currentWeapon]="this.weapon"
                        [selectedCharactersId]="weapon && weapon.enabledCharactersId"
                        (selectedCharactersIdChange)="changeSelectedId($event, 'enabledCharactersId')">
                  </enabled-character-selector>
                  <br />
                  <h5>Enabled Classes:<i class="pe-7s-info" style="position: relative" placement='top' delay='250'
                              ttWidth="auto" ttAlign="center" ttPadding="15px"
                              tooltip="Selects the classes with the ability to use the weapon."></i></h5>
                  <enabled-class-selector (parentEvent)="ngAfterViewInit()" [currentWeapon]="this.weapon"
                        [selectedClassessId]="weapon && weapon.enabledClassesId"
                        (selectedClassessIdChange)="changeSelectedId($event, 'enabledClassesId')">
                  </enabled-class-selector>
            </div>
      </div>