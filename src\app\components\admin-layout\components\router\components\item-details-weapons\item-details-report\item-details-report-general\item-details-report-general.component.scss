
h4
{
  display: inline;
}

input[type='checkbox']
{
  margin-left: 10px;
  margin-bottom: 10px;
  width: 15px;
  height: 15px;
}

*[data-tooltip] 
{
  position: relative;
}

*[data-tooltip]::after 
{
  content: attr(data-tooltip);
  color:white;
  position: absolute;
  right: -120px;
  width: 200px;
  
  pointer-events: none;
  opacity: 0;
  -webkit-transition: opacity .15s ease-in-out;
  -moz-transition: opacity .15s ease-in-out;
  -ms-transition: opacity .15s ease-in-out;
  -o-transition: opacity .15s ease-in-out;
  transition: opacity .15s ease-in-out;
  
  display: block;	
  top: 100%;	
  font-size: 13px;	
  font-family: Arial, Helvetica, sans-serif;	
  line-height: 16px;	
  background: #4e4e4e;	
  padding: 2px 2px;	
  border-radius: 3px;	
  border: 1px solid #4e4e4e;	
  box-shadow: 2px 4px 5px rgba(0, 0, 0, 0.4);
  top: -50px;
}

*[data-tooltip]:hover::after
{
  opacity: 1;
}

/* Weapon Type Badge Styles */
.weapon-type-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.weapon-type-badge {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  cursor: default;
  user-select: none;
  min-width: 110px;
  text-align: center;
}

/* Common Weapon Style - Subtle gray/silver */
.weapon-type-common {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  color: #5f6368;
  border-color: #dadce0;
}

/* Special Weapon Style - Premium golden */
.weapon-type-special {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #8b4513;
  border-color: #daa520;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}