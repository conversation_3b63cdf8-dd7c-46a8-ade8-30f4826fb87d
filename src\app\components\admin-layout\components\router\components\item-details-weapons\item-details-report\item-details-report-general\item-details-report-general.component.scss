
h4
{
  display: inline;
}

input[type='checkbox']
{
  margin-left: 10px;
  margin-bottom: 10px;
  width: 15px;
  height: 15px;
}

*[data-tooltip] 
{
  position: relative;
}

*[data-tooltip]::after 
{
  content: attr(data-tooltip);
  color:white;
  position: absolute;
  right: -120px;
  width: 200px;
  
  pointer-events: none;
  opacity: 0;
  -webkit-transition: opacity .15s ease-in-out;
  -moz-transition: opacity .15s ease-in-out;
  -ms-transition: opacity .15s ease-in-out;
  -o-transition: opacity .15s ease-in-out;
  transition: opacity .15s ease-in-out;
  
  display: block;	
  top: 100%;	
  font-size: 13px;	
  font-family: Arial, Helvetica, sans-serif;	
  line-height: 16px;	
  background: #4e4e4e;	
  padding: 2px 2px;	
  border-radius: 3px;	
  border: 1px solid #4e4e4e;	
  box-shadow: 2px 4px 5px rgba(0, 0, 0, 0.4);
  top: -50px;
}

*[data-tooltip]:hover::after
{
  opacity: 1;
}

/* Weapon Type Badge Styles */
.weapon-type-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.weapon-type-badge {
  display: inline-block;
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  cursor: default;
  user-select: none;
  min-width: 120px;
  text-align: center;
}

.weapon-type-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.18);
}

/* Common Weapon Style - Subtle gray/silver with modern look */
.weapon-type-common {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  color: #5f6368;
  border-color: #dadce0;
  position: relative;
  overflow: hidden;
}

.weapon-type-common::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.weapon-type-common:hover {
  background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
  border-color: #bdc1c6;
  color: #3c4043;
}

.weapon-type-common:hover::before {
  left: 100%;
}

/* Special Weapon Style - Premium golden with shine effect */
.weapon-type-special {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #8b4513;
  border-color: #daa520;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.weapon-type-special::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transition: left 0.5s;
}

.weapon-type-special:hover {
  background: linear-gradient(135deg, #ffed4e 0%, #ffc947 100%);
  border-color: #b8860b;
  color: #654321;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.weapon-type-special:hover::before {
  left: 100%;
}